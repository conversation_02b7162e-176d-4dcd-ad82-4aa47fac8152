[project]
name = "hugging"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "en-core-web-sm",
    "memory-profiler>=0.61.0",
    "numpy>=1.21.0",
    "spacy>=3.8.7",
    "tensorflow>=2.19.0",
    "tf-keras>=2.19.0",
    "transformers>=4.52.4",
]

[tool.uv.sources]
en-core-web-sm = { url = "https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.8.0/en_core_web_sm-3.8.0-py3-none-any.whl" }
